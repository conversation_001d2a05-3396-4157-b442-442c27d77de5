{"name": "ip-creator-server", "version": "1.0.0", "description": "IP创造师服务器端 - 支持长时间队列任务处理", "main": "dist/server.js", "scripts": {"build": "next build", "start": "next start -p 3000", "dev": "next dev -p 3000", "worker": "node dist/workers/image-generation-worker.js", "worker:dev": "ts-node workers/image-generation-worker.ts", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:logs": "pm2 logs", "setup": "chmod +x setup.sh && ./setup.sh", "deploy": "npm run build && npm run pm2:restart"}, "dependencies": {"@next/bundle-analyzer": "^15.2.4", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@types/node": "^22.8.1", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.451.0", "next": "15.2.4", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "typescript": "^5.6.3", "bull": "^4.12.9", "ioredis": "^5.4.1", "@types/bull": "^4.10.0", "node-fetch": "^3.3.2", "@types/node-fetch": "^2.6.11", "form-data": "^4.0.0", "@types/form-data": "^2.5.0", "sharp": "^0.33.5", "uuid": "^10.0.0", "@types/uuid": "^10.0.0"}, "devDependencies": {"@types/eslint": "^9.6.1", "eslint": "^8.57.1", "eslint-config-next": "15.2.4", "postcss": "^8.4.49", "tailwindcss": "^3.4.14", "ts-node": "^10.9.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["nextjs", "ai", "image-generation", "queue", "redis", "bull"], "author": "IP Creator Team", "license": "MIT"}