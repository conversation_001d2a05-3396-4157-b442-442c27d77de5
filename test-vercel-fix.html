<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vercel 修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(34, 197, 94, 0.2); border: 2px solid #22c55e; }
        .warning { background: rgba(251, 191, 36, 0.2); border: 2px solid #fbbf24; }
        .error { background: rgba(239, 68, 68, 0.2); border: 2px solid #ef4444; }
        .info { background: rgba(59, 130, 246, 0.2); border: 2px solid #3b82f6; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .test-section h3 {
            margin-top: 0;
            color: #fbbf24;
        }
        .test-results {
            max-height: 400px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        .file-input {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed #fbbf24;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin: 15px 0;
            cursor: pointer;
            transition: all 0.3s;
        }
        .file-input:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #60a5fa;
        }
        .file-input input {
            display: none;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .comparison-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 8px;
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #fbbf24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vercel 文件处理修复验证</h1>
        
        <div class="status info">
            <strong>🎯 修复目标:</strong> 解决 "a.arrayBuffer is not a function" 错误<br>
            <strong>📅 修复时间:</strong> <span id="deployTime">检查中...</span><br>
            <strong>🌐 测试环境:</strong> <span id="testEnv">生产环境</span>
        </div>

        <div class="comparison">
            <div class="comparison-item">
                <h4>❌ 修复前</h4>
                <p>• File.arrayBuffer() 方法不可用<br>
                • 500 错误: "a.arrayBuffer is not a function"<br>
                • 所有图片生成请求失败<br>
                • 用户无法使用应用</p>
            </div>
            
            <div class="comparison-item">
                <h4>✅ 修复后</h4>
                <p>• 多种文件处理方法备用<br>
                • 兼容 Vercel 无服务器环境<br>
                • 详细错误日志和调试信息<br>
                • 图片生成功能正常工作</p>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试步骤</h3>
            
            <div class="file-input" onclick="document.getElementById('testImage').click()">
                <input type="file" id="testImage" accept="image/*" onchange="handleFileSelect(event)">
                <p>📁 点击选择测试图片</p>
                <p style="font-size: 14px; opacity: 0.8;">支持 JPG, PNG 格式</p>
            </div>

            <div>
                <button onclick="testLocalAPI()">🧪 测试本地 API</button>
                <button onclick="testProductionAPI()">🌐 测试生产环境</button>
                <button onclick="checkDeploymentStatus()">📊 检查部署状态</button>
                <button onclick="clearResults()">🧹 清理结果</button>
            </div>
        </div>

        <div class="test-results" id="testResults"></div>
    </div>

    <script>
        const PROD_URL = 'https://ip-creator-ziyerrs-projects.vercel.app';
        const LOCAL_URL = 'http://localhost:3000';
        let selectedFile = null;

        function updateStatus(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                updateStatus(`📁 已选择文件: ${file.name} (${(file.size / 1024).toFixed(1)}KB)`, 'info');
                
                // 显示文件信息
                updateStatus(`📋 文件详情: 类型=${file.type}, 大小=${file.size}字节`, 'info');
                
                // 检查文件对象的方法
                const methods = {
                    arrayBuffer: typeof file.arrayBuffer === 'function',
                    stream: typeof file.stream === 'function',
                    text: typeof file.text === 'function'
                };
                
                updateStatus(`🔍 文件对象方法: ${JSON.stringify(methods)}`, 'info');
            }
        }

        async function testAPI(baseUrl, envName) {
            if (!selectedFile) {
                updateStatus('❌ 请先选择一个测试图片', 'error');
                return;
            }

            updateStatus(`🧪 开始测试 ${envName} API...`, 'info');
            
            try {
                const formData = new FormData();
                formData.append('prompt', '可爱的卡通风格头像');
                formData.append('image', selectedFile);
                formData.append('variationSeed', '0');

                updateStatus(`📡 发送请求到: ${baseUrl}/api/generate-single-image`, 'info');
                
                const startTime = Date.now();
                const response = await fetch(`${baseUrl}/api/generate-single-image`, {
                    method: 'POST',
                    body: formData
                });
                
                const endTime = Date.now();
                const duration = endTime - startTime;
                
                updateStatus(`⏱️ 请求耗时: ${duration}ms`, 'info');
                updateStatus(`📊 响应状态: ${response.status} ${response.statusText}`, response.ok ? 'success' : 'error');
                
                const result = await response.json();
                
                if (response.ok) {
                    updateStatus(`✅ ${envName} API 测试成功!`, 'success');
                    updateStatus(`🎯 返回数据: ${JSON.stringify(result).substring(0, 200)}...`, 'success');
                    
                    if (result.url) {
                        updateStatus(`🖼️ 图片URL: ${result.url}`, 'success');
                        
                        // 尝试加载图片验证
                        const img = new Image();
                        img.onload = () => {
                            updateStatus(`✅ 图片加载验证成功`, 'success');
                        };
                        img.onerror = () => {
                            updateStatus(`⚠️ 图片URL无法加载`, 'warning');
                        };
                        img.src = result.url.startsWith('/') ? baseUrl + result.url : result.url;
                    }
                } else {
                    updateStatus(`❌ ${envName} API 测试失败`, 'error');
                    updateStatus(`💥 错误详情: ${JSON.stringify(result)}`, 'error');
                    
                    // 分析错误类型
                    if (result.details && result.details.includes('arrayBuffer')) {
                        updateStatus(`🔍 检测到 arrayBuffer 错误 - 修复可能未生效`, 'error');
                    }
                }
                
            } catch (error) {
                updateStatus(`❌ ${envName} API 请求异常: ${error.message}`, 'error');
                
                if (error.message.includes('Failed to fetch')) {
                    updateStatus(`🌐 网络连接问题或 CORS 错误`, 'warning');
                }
            }
        }

        async function testLocalAPI() {
            await testAPI(LOCAL_URL, '本地环境');
        }

        async function testProductionAPI() {
            await testAPI(PROD_URL, '生产环境');
        }

        async function checkDeploymentStatus() {
            updateStatus('📊 检查部署状态...', 'info');
            
            try {
                // 检查生产环境可访问性
                const response = await fetch(PROD_URL, { method: 'HEAD', mode: 'no-cors' });
                updateStatus('✅ 生产环境可访问', 'success');
                
                // 检查 API 端点
                const apiResponse = await fetch(`${PROD_URL}/api/generate-single-image`, { 
                    method: 'HEAD', 
                    mode: 'no-cors' 
                });
                updateStatus('✅ API 端点可访问', 'success');
                
                // 更新部署时间
                document.getElementById('deployTime').textContent = new Date().toLocaleString();
                
            } catch (error) {
                updateStatus('❌ 部署状态检查失败: ' + error.message, 'error');
            }
        }

        function clearResults() {
            document.getElementById('testResults').innerHTML = '';
            updateStatus('🧹 测试结果已清理', 'info');
        }

        // 页面加载时自动检查
        window.onload = function() {
            updateStatus('🚀 Vercel 修复验证工具已加载', 'info');
            updateStatus('📝 请选择一个测试图片，然后点击测试按钮', 'info');
            
            // 自动检查部署状态
            setTimeout(checkDeploymentStatus, 1000);
        };
    </script>
</body>
</html>
