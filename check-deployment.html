<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vercel 部署状态检查</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            font-weight: bold;
        }
        .success { background: rgba(34, 197, 94, 0.2); border: 2px solid #22c55e; }
        .warning { background: rgba(251, 191, 36, 0.2); border: 2px solid #fbbf24; }
        .error { background: rgba(239, 68, 68, 0.2); border: 2px solid #ef4444; }
        .info { background: rgba(59, 130, 246, 0.2); border: 2px solid #3b82f6; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-weight: bold;
            margin: 5px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .deployment-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .deployment-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 8px;
        }
        .deployment-item h3 {
            margin-top: 0;
            color: #fbbf24;
        }
        .url-link {
            color: #60a5fa;
            text-decoration: none;
            font-weight: bold;
        }
        .url-link:hover {
            color: #93c5fd;
            text-decoration: underline;
        }
        .test-results {
            max-height: 300px;
            overflow-y: auto;
            background: rgba(0, 0, 0, 0.2);
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Vercel 自动部署状态</h1>
        
        <div class="deployment-info">
            <div class="deployment-item">
                <h3>📦 生产环境</h3>
                <p><strong>URL:</strong> <a href="https://ip-creator-ziyerrs-projects.vercel.app" target="_blank" class="url-link">ip-creator-ziyerrs-projects.vercel.app</a></p>
                <p><strong>状态:</strong> <span id="prodStatus">检查中...</span></p>
                <p><strong>最后更新:</strong> <span id="prodUpdated">-</span></p>
            </div>
            
            <div class="deployment-item">
                <h3>🔧 开发环境</h3>
                <p><strong>本地:</strong> <a href="http://localhost:3000" target="_blank" class="url-link">localhost:3000</a></p>
                <p><strong>状态:</strong> <span id="devStatus">检查中...</span></p>
                <p><strong>Git 分支:</strong> main</p>
            </div>
        </div>

        <div class="status info">
            <strong>🔄 自动部署流程:</strong><br>
            1. 推送代码到 GitHub main 分支<br>
            2. Vercel 自动检测更改<br>
            3. 构建和部署新版本<br>
            4. 更新生产环境 URL
        </div>

        <div>
            <button onclick="checkDeployment()">🔍 检查部署状态</button>
            <button onclick="testAPI()">🧪 测试 API</button>
            <button onclick="openProduction()">🌐 打开生产环境</button>
            <button onclick="openLocal()">💻 打开本地环境</button>
        </div>

        <div class="test-results" id="testResults"></div>
    </div>

    <script>
        const PROD_URL = 'https://ip-creator-ziyerrs-projects.vercel.app';
        const LOCAL_URL = 'http://localhost:3000';

        function updateStatus(message, type = 'info') {
            const results = document.getElementById('testResults');
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            results.appendChild(div);
            results.scrollTop = results.scrollHeight;
        }

        async function checkDeployment() {
            updateStatus('🔍 开始检查部署状态...', 'info');
            
            // 检查生产环境
            try {
                updateStatus('📡 检查生产环境...', 'info');
                const prodResponse = await fetch(PROD_URL, { 
                    method: 'HEAD',
                    mode: 'no-cors' // 避免 CORS 问题
                });
                
                document.getElementById('prodStatus').textContent = '✅ 在线';
                document.getElementById('prodUpdated').textContent = new Date().toLocaleString();
                updateStatus('✅ 生产环境正常运行', 'success');
                
            } catch (error) {
                document.getElementById('prodStatus').textContent = '❌ 离线';
                updateStatus('❌ 生产环境检查失败: ' + error.message, 'error');
            }

            // 检查本地环境
            try {
                updateStatus('💻 检查本地环境...', 'info');
                const localResponse = await fetch(LOCAL_URL, { 
                    method: 'HEAD',
                    mode: 'no-cors'
                });
                
                document.getElementById('devStatus').textContent = '✅ 运行中';
                updateStatus('✅ 本地环境正常运行', 'success');
                
            } catch (error) {
                document.getElementById('devStatus').textContent = '❌ 未运行';
                updateStatus('⚠️ 本地环境未运行或不可访问', 'warning');
            }
        }

        async function testAPI() {
            updateStatus('🧪 开始测试 API 端点...', 'info');
            
            const endpoints = [
                '/api/generate-single-image',
                '/outputs/'
            ];

            for (const endpoint of endpoints) {
                try {
                    updateStatus(`📡 测试 ${endpoint}...`, 'info');
                    
                    // 测试生产环境
                    const prodUrl = PROD_URL + endpoint;
                    const response = await fetch(prodUrl, { 
                        method: 'HEAD',
                        mode: 'no-cors'
                    });
                    
                    updateStatus(`✅ 生产环境 ${endpoint} 可访问`, 'success');
                    
                } catch (error) {
                    updateStatus(`❌ ${endpoint} 测试失败: ${error.message}`, 'error');
                }
            }
        }

        function openProduction() {
            updateStatus('🌐 打开生产环境...', 'info');
            window.open(PROD_URL, '_blank');
        }

        function openLocal() {
            updateStatus('💻 打开本地环境...', 'info');
            window.open(LOCAL_URL, '_blank');
        }

        // 页面加载时自动检查
        window.onload = function() {
            updateStatus('🚀 部署状态检查工具已加载', 'info');
            updateStatus('📝 Vercel 已配置自动部署，每次推送到 main 分支都会触发部署', 'info');
            
            // 延迟检查，给页面时间加载
            setTimeout(checkDeployment, 1000);
        };
    </script>
</body>
</html>
