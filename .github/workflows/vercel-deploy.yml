name: Vercel Production Deployment

env:
  VERCEL_ORG_ID: ${{ secrets.VERCEL_ORG_ID }}
  VERCEL_PROJECT_ID: ${{ secrets.VERCEL_PROJECT_ID }}

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  Deploy-Production:
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build project
        run: npm run build
        
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=production --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Build Project Artifacts
        run: vercel build --prod --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --prod --token=${{ secrets.VERCEL_TOKEN }}

  Deploy-Preview:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build project
        run: npm run build
        
      - name: Install Vercel CLI
        run: npm install --global vercel@latest
        
      - name: Pull Vercel Environment Information
        run: vercel pull --yes --environment=preview --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Build Project Artifacts
        run: vercel build --token=${{ secrets.VERCEL_TOKEN }}
        
      - name: Deploy Project Artifacts to Vercel
        run: vercel deploy --prebuilt --token=${{ secrets.VERCEL_TOKEN }}

  Health-Check:
    runs-on: ubuntu-latest
    needs: Deploy-Production
    if: github.ref == 'refs/heads/main'
    steps:
      - name: Health Check
        run: |
          echo "Waiting for deployment to be ready..."
          sleep 30
          
          # Health check
          response=$(curl -s -o /dev/null -w "%{http_code}" https://ip-creator-ziyerrs-projects.vercel.app)
          if [ $response -eq 200 ]; then
            echo "✅ Deployment successful! Site is responding with HTTP 200"
          else
            echo "❌ Deployment may have issues. HTTP response: $response"
            exit 1
          fi
          
      - name: API Health Check
        run: |
          echo "Testing API endpoints..."
          
          # Test if API endpoints are accessible (expect 405 for GET requests)
          api_response=$(curl -s -o /dev/null -w "%{http_code}" https://ip-creator-ziyerrs-projects.vercel.app/api/generate-single-image)
          if [ $api_response -eq 405 ] || [ $api_response -eq 200 ]; then
            echo "✅ API endpoint is accessible"
          else
            echo "⚠️ API endpoint response: $api_response"
          fi
